import { useRef, useState } from "react";
import { EditorState } from "../types";
import { Preview } from "./preview";
import { Toolbar } from "./toolbar";
import { Features } from "./features";
import { HowTo } from "./howto";
import { FAQ } from "./faq";
import { About } from "./about";
import { Testimonials } from "./testimonials";
import { CTA } from "./cta";
import { useRandomSound } from "../utils";

export function Home() {
  useRandomSound(0.001);

  const [state, setState] = useState<EditorState>({
    text: "Invincible",
    color: "#ebed00",
    showCredits: true,
    showWatermark: true,
    background: "url('/backgrounds/blue.jpg') no-repeat center center / cover",
    fontSize: 24,
    outline: 0,
    subtitleOffset: 0,
    outlineColor: "black",
    effect: null,
    generating: false,
    smallSubtitle: "BASED ON THE COMIC BOOK BY",
    subtitle: "<PERSON>, <PERSON>, & <PERSON>",
  });

  const canvasRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-900 to-slate-950 py-12 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
            Invincible Title Card Maker
          </h1>
          <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-5xl mx-auto">
            Design professional Invincible title card graphics with our free online editor. Customize text, colors, and backgrounds to create stunning title cards inspired by the hit animated series.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#editor" className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-8 rounded-lg transition-colors">
              Start Creating
            </a>
            <a href="#faq" className="border border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black font-bold py-3 px-8 rounded-lg transition-colors">
              View FAQ
            </a>
          </div>
        </div>
      </section>

      {/* Editor Section */}
      <main id="main-content" className="flex-1">
        <section id="editor" className="editor-section">
          <div className="flex-1 flex flex-col lg:flex-row">
            {/* Preview Area - Left Side */}
            <div className="flex-1 lg:flex-[2] flex flex-col justify-center p-4 lg:p-8 preview-area">
              <div className="w-full max-w-5xl mx-auto">
                <Preview canvasRef={canvasRef} state={state} />
                {/* <AdBanner
                  data-ad-format="fluid"
                  data-ad-slot="6767948661"
                  data-full-width-responsive="true"
                  style={{
                    width: "100%",
                    minHeight: 100,
                    maxHeight: 100,
                  }}
                /> */}
              </div>
            </div>

            {/* Toolbar Area - Right Side */}
            <div className="lg:flex-1 lg:min-h-screen toolbar-area border-l border-slate-700/50">
              <div className="h-full flex flex-col">
                <Toolbar canvasRef={canvasRef} state={state} setState={setState} />
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Features Section */}
      <Features />

      {/* How To Use Section */}
      <HowTo />

      {/* FAQ Section */}
      <FAQ />

      {/* About Section */}
      <About />

      {/* Testimonials Section */}
      <Testimonials />

      {/* CTA Section */}
      <CTA />
    </>
  );
}
